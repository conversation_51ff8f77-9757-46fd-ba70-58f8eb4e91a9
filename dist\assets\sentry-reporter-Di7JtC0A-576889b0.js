import{a as O,f as _,s as v,b as P,d as M,e as D}from"./index-5e904b1c.js";const I="https://api.pincaimao.com/agents/platform",L=(e,t,r)=>{const s=e.get(t);s?s.includes(r)||s.push(r):e.set(t,[r])},q=(e,t)=>{let r;return(...s)=>{r&&clearTimeout(r),r=setTimeout(()=>{r=0,e(...s)},t)}},x=e=>!("isConnected"in e)||e.isConnected,z=q(e=>{for(let t of e.keys())e.set(t,e.get(t).filter(x))},2e3),J=()=>{if(typeof O!="function")return{};const e=new Map;return{dispose:()=>e.clear(),get:t=>{const r=O();r&&L(e,t,r)},set:t=>{const r=e.get(t);r&&e.set(t,r.filter(_)),z(e)},reset:()=>{e.forEach(t=>t.forEach(_)),z(e)}}},C=e=>typeof e=="function"?e():e,F=(e,t=(r,s)=>r!==s)=>{const r=C(e);let s=new Map(Object.entries(r??{}));const o={dispose:[],get:[],set:[],reset:[]},a=()=>{s=new Map(Object.entries(C(e)??{})),o.reset.forEach(n=>n())},d=()=>{o.dispose.forEach(n=>n()),a()},h=n=>(o.get.forEach(c=>c(n)),s.get(n)),m=(n,c)=>{const i=s.get(n);t(c,i,n)&&(s.set(n,c),o.set.forEach(l=>l(n,c,i)))},u=typeof Proxy>"u"?{}:new Proxy(r,{get(n,c){return h(c)},ownKeys(n){return Array.from(s.keys())},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},has(n,c){return s.has(c)},set(n,c,i){return m(c,i),!0}}),f=(n,c)=>(o[n].push(c),()=>{G(o[n],c)});return{state:u,get:h,set:m,on:f,onChange:(n,c)=>{const i=f("set",(S,E)=>{S===n&&c(E)}),l=f("reset",()=>c(C(e)[n]));return()=>{i(),l()}},use:(...n)=>{const c=n.reduce((i,l)=>(l.set&&i.push(f("set",l.set)),l.get&&i.push(f("get",l.get)),l.reset&&i.push(f("reset",l.reset)),l.dispose&&i.push(f("dispose",l.dispose)),i),[]);return()=>c.forEach(i=>i())},dispose:d,reset:a,forceUpdate:n=>{const c=s.get(n);o.set.forEach(i=>i(n,c,c))}}},G=(e,t)=>{const r=e.indexOf(t);r>=0&&(e[r]=e[e.length-1],e.length--)},B=(e,t)=>{const r=F(e,t);return r.use(J()),r},{state:$,onChange:W}=B({token:localStorage.getItem("pcm-sdk-auth-token")||null}),K={getToken:()=>$.token,setToken:e=>{$.token=e,localStorage.setItem("pcm-sdk-auth-token",e)},clearToken:()=>{$.token=null,localStorage.removeItem("pcm-sdk-auth-token")}};W("token",e=>{e?localStorage.setItem("pcm-sdk-auth-token",e):localStorage.removeItem("pcm-sdk-auth-token")});const V=()=>{try{const e=localStorage.getItem("pcm-sdk-config-data");return e?JSON.parse(e):{}}catch(e){return console.error("Error parsing stored config:",e),{}}},{state:y,onChange:Q}=B({data:V()}),A={getConfig:()=>y.data,getItem:(e,t)=>y.data[e]!==void 0?y.data[e]:t,setItem:(e,t)=>{y.data={...y.data,[e]:t}},removeItem:e=>{const t={...y.data};delete t[e],y.data=t},clear:()=>{y.data={}},updateConfig:e=>{y.data={...y.data,...e}}};Q("data",e=>{try{Object.keys(e).length>0?localStorage.setItem("pcm-sdk-config-data",JSON.stringify(e)):localStorage.removeItem("pcm-sdk-config-data")}catch(t){console.error("Error saving config to localStorage:",t)}});const b=()=>K.getToken()||"",N=3*60*1e3,T=async(e,t,r=N)=>{const s=new AbortController,o=setTimeout(()=>s.abort(),r);try{const a=await fetch(e,{...t,signal:s.signal});return clearTimeout(o),a}catch(a){throw clearTimeout(o),a instanceof Error&&a.name==="AbortError"?new Error("请求超时，请检查网络连接后重试"):a}},R=async(e,t=!1)=>{var u;const{url:r,method:s,headers:o={},data:a,onMessage:d,onError:h,onComplete:m}=e;try{const f=`${I}${r}`;if(!o.authorization&&!o.Authorization){const c=b();c&&(o.authorization=`Bearer ${c}`)}const w=await T(f,{method:s,headers:{Accept:"text/event-stream","Content-Type":"application/json",...o},body:a?JSON.stringify(a):void 0});if(w.status===401&&(U(),!t))return R(e,!0);w.ok||console.error(`HTTP error! status: ${w.status}`);const g=(u=w.body)==null?void 0:u.getReader();if(!g)throw new Error("No reader available");const p=new TextDecoder;let n="";for(;;){const{value:c,done:i}=await g.read();if(i)break;n+=p.decode(c,{stream:!0});const l=n.split(`
`);n=l.pop()||"";for(const S of l)if(S.startsWith("data:"))try{const E=S.substring(5).trim();if(!E)continue;const H=JSON.parse(E);d==null||d(H)}catch(E){console.error("解析 SSE 数据错误:",E,"原始数据:",S)}}m==null||m()}catch(f){if(console.error("SSE 请求错误:",f),!t)return R(e,!0);h==null||h(f)}},U=()=>{const e=new CustomEvent("pcm-token-invalid",{bubbles:!0,composed:!0,detail:{timestamp:new Date().getTime()}});document.dispatchEvent(e)},k=async(e,t=!0)=>{const{url:r,method:s="GET",headers:o={},params:a={},data:d,formData:h,onMessage:m}=e;try{const u=new URLSearchParams;Object.entries(a).forEach(([n,c])=>{c!=null&&u.append(n,String(c))});let f=`${I}${r}${u.toString()?`?${u.toString()}`:""}`;if(o.authorization==null&&o.Authorization==null){const n=b();n&&(o.authorization=`Bearer ${n}`)}const w={method:s,headers:h?{...o}:{"Content-Type":"application/json",...o}};if(h?w.body=h:s!=="GET"&&s!=="HEAD"&&d&&(w.body=JSON.stringify(d)),s==="GET"&&d){const n=new URLSearchParams;Object.entries(d).forEach(([c,i])=>{i!=null&&n.append(c,String(i))}),f+=(u.toString()?"&":"?")+n.toString()}const g=await T(f,w);if(g.status===401&&(U(),t))return k(e,!1);const p=await g.json();return m&&m(p),g.ok?p.code!==0?(console.error(`API错误: ${p.message}`),{success:!1,message:p.message,error:p}):{success:!0,data:p.data}:(console.error(`HTTP错误: ${g.status} ${g.statusText}`),{success:!1,message:p.message||`HTTP错误: ${g.status}`,error:p})}catch(u){return console.error("HTTP请求错误:",u),t?k(e,!1):(e.onError&&e.onError(u),{success:!1,error:u,message:u instanceof Error?u.message:"未知错误"})}finally{e.onComplete&&e.onComplete()}},ee=async e=>{const t=await k({url:"/sdk/v1/user",method:"GET",headers:{Authorization:`Bearer ${e}`}},!1);return!t.success&&t.error&&t.error.code===401?!1:(A.setItem("pcm-sdk-CUser",`${t.data.user}(${t.data.chat_user})`),t.success)},te=async e=>{if(!e)throw new Error("智能体ID不能为空");try{const t=await k({url:`/sdk/v1/agent/${e}/info`,method:"GET"});if(!t.success)throw new Error(t.message||"获取智能体信息失败");return t.data}catch(t){throw console.error("获取智能体信息失败:",t),t}},X=async e=>new Promise((t,r)=>{const s=new FileReader;s.onload=async o=>{var a;try{const d=(a=o.target)==null?void 0:a.result,h=await crypto.subtle.digest("SHA-256",d),u=Array.from(new Uint8Array(h)).map(f=>f.toString(16).padStart(2,"0")).join("");t(u)}catch(d){r(d)}},s.onerror=()=>r(new Error("读取文件失败")),s.readAsArrayBuffer(e)}),Y=async(e,t,r="application/octet-stream",s=2)=>{let o=0;for(;;)try{const a=await T(e,{method:"PUT",body:t,headers:{"Content-Type":r}});if(!a.ok)throw new Error(`文件上传失败: ${a.status} ${a.statusText}`);return a}catch(a){if(o++,console.error(`文件上传错误(尝试 ${o}/${s}):`,a),o>=s)throw a}},re=async(e,t,r)=>{try{const s=await X(e),o=await k({url:"/sdk/v1/files/generate-upload-url",method:"POST",data:{...r,filename:e.name,filesize:e.size,sha256:s},headers:t});if(!o.success||!o.data)throw new Error(o.message||"获取上传URL失败");const a=o.data;if(a.is_deleted!=1){const d=await Y(a.upload_url,e,a.content_type||"application/octet-stream");if(!d.ok)throw new Error(`文件上传到腾讯云失败: ${d.status} ${d.statusText}`);k({url:"/sdk/v1/files/mark-as-upload",method:"POST",data:{cos_key:a.cos.cos_key},headers:t})}return{cos_key:a.cos.cos_key,file_name:a.filename,file_size:a.filesize,ext:a.filetype}}catch(s){throw console.error("文件上传错误:",s),s}},j=async(e,t,r=!1)=>{const s=b();if(!s)throw new Error("API密钥不能为空");try{const o=await T(`${I}/sdk/v1/tts/synthesize_audio`,{method:"POST",headers:{"Content-Type":"application/json",authorization:"Bearer "+s},body:JSON.stringify({text:e})});if(o.status===401&&(U(),!r))return j(e,t,!0);if(!o.ok)throw new Error("语音合成失败");const a=await o.blob();return URL.createObjectURL(a)}catch(o){if(console.error("语音合成错误:",o),!r)return j(e,t,!0);throw o}};class se{static emitError(t){const r=new CustomEvent("pcm-error",{bubbles:!0,composed:!0,detail:t});document.dispatchEvent(r)}static addErrorListener(t){const r=s=>{t(s.detail)};return document.addEventListener("pcm-error",r),()=>{document.removeEventListener("pcm-error",r)}}}class oe{static captureError(t,r){try{const s=A.getItem("pcm-sdk-CUser");s&&v({id:String(s)});let o=t;if(r!=null&&r.title&&(o=new Error(r.title),t!=null&&t.stack&&(o.stack=t.stack),o.cause=t),r){const{title:a,...d}=r;P("error_context",d)}M(o)}catch(s){console.error("Sentry 上报错误失败:",s)}}static captureMessage(t,r){try{const s=A.getItem("pcm-sdk-CUser");s&&v({id:String(s)}),r&&P("message_context",r),D(t)}catch(s){console.error("Sentry 上报消息失败:",s)}}}export{se as E,oe as S,K as a,k as b,A as c,j as d,te as f,R as s,re as u,ee as v};
