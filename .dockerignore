# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出（如果使用预构建方式则保留dist）
# dist

# 开发工具
.vscode
.idea
*.swp
*.swo

# 版本控制
.git
.gitignore

# 环境变量文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 临时文件
.tmp
.temp

# 操作系统文件
.DS_Store
Thumbs.db

# 测试覆盖率
coverage

# Docker相关
Dockerfile
.dockerignore
docker-compose.yml

# 文档
README.md
*.md

# 其他
.eslintcache
