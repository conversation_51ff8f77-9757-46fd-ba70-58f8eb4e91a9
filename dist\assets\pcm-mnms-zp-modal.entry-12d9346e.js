var F=Object.defineProperty;var y=(e,o,n)=>o in e?F(e,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[o]=n;var t=(e,o,n)=>(y(e,typeof o!="symbol"?o+"":o,n),n);import{r as C,c as s,g as I,h as i}from"./index-5e904b1c.js";import{a,v as k,c as E,E as l,u as S,S as x}from"./sentry-reporter-Di7JtC0A-576889b0.js";const j="",D=":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}",B=class{constructor(e){t(this,"modalTitle","模拟面试");t(this,"token");t(this,"isOpen",!1);t(this,"modalClosed");t(this,"icon");t(this,"zIndex",1e3);t(this,"isShowHeader",!0);t(this,"isNeedClose",!0);t(this,"conversationId");t(this,"defaultQuery","请开始模拟面试");t(this,"fullscreen",!1);t(this,"customInputs",{});t(this,"uploadSuccess");t(this,"streamComplete");t(this,"conversationStart");t(this,"interviewComplete");t(this,"tokenInvalid");t(this,"someErrorEvent");t(this,"filePreviewMode","window");t(this,"interviewMode","text");t(this,"recordingError");t(this,"showCopyButton",!0);t(this,"showFeedbackButtons",!0);t(this,"selectedFile",null);t(this,"isUploading",!1);t(this,"uploadedFileInfo",null);t(this,"showChatModal",!1);t(this,"jobDescription","");t(this,"isSubmitting",!1);t(this,"tokenInvalidListener");t(this,"removeErrorListener");t(this,"handleClose",()=>{this.modalClosed.emit()});t(this,"handleFileChange",e=>{const o=e.target;o.files&&o.files.length>0&&(this.selectedFile=o.files[0])});t(this,"handleUploadClick",()=>{var o;const e=(o=this.hostElement.shadowRoot)==null?void 0:o.querySelector(".file-input");e==null||e.click()});t(this,"clearSelectedFile",()=>{var o;this.selectedFile=null,this.uploadedFileInfo=null;const e=(o=this.hostElement.shadowRoot)==null?void 0:o.querySelector(".file-input");e&&(e.value="")});t(this,"handleJobDescriptionChange",e=>{const o=e.target;this.jobDescription=o.value});t(this,"handleStartInterview",async()=>{var e;if(!this.selectedFile){alert("请上传简历");return}if(!((e=this.customInputs)!=null&&e.job_info)&&!this.jobDescription.trim()){alert("请输入职位描述");return}this.isSubmitting=!0;try{if(!this.uploadedFileInfo&&(await this.uploadFile(),!this.uploadedFileInfo)){this.isSubmitting=!1;return}this.showChatModal=!0}catch(o){console.error("开始面试时出错:",o),x.captureError(o,{action:"handleStartInterview",component:"pcm-mnms-zp-modal",title:"开始面试时出错"}),l.emitError({error:o,message:"开始面试时出错，请重试"})}finally{this.isSubmitting=!1}});C(this,e),this.modalClosed=s(this,"modalClosed"),this.uploadSuccess=s(this,"uploadSuccess"),this.streamComplete=s(this,"streamComplete"),this.conversationStart=s(this,"conversationStart"),this.interviewComplete=s(this,"interviewComplete"),this.tokenInvalid=s(this,"tokenInvalid"),this.someErrorEvent=s(this,"someErrorEvent"),this.recordingError=s(this,"recordingError")}get hostElement(){return I(this)}handleTokenChange(e){e&&e!==a.getToken()&&a.setToken(e)}async handleIsOpenChange(e){var o,n;e?(this.customInputs&&this.customInputs.job_info&&(this.jobDescription=this.customInputs.job_info),await k(this.token),((o=this.customInputs)!=null&&o.file_url&&((n=this.customInputs)!=null&&n.job_info)||this.conversationId)&&(this.showChatModal=!0)):(this.clearSelectedFile(),this.showChatModal=!1,this.jobDescription="")}componentWillLoad(){this.zIndex&&E.setItem("modal-zIndex",this.zIndex),this.token&&a.setToken(this.token),this.tokenInvalidListener=()=>{this.tokenInvalid.emit()},this.removeErrorListener=l.addErrorListener(e=>{this.someErrorEvent.emit(e)}),document.addEventListener("pcm-token-invalid",this.tokenInvalidListener)}disconnectedCallback(){document.removeEventListener("pcm-token-invalid",this.tokenInvalidListener),this.removeErrorListener&&this.removeErrorListener()}async uploadFile(){if(this.selectedFile){this.isUploading=!0;try{const e=await S(this.selectedFile,{},{tags:["resume"]});this.uploadedFileInfo=e,this.uploadSuccess.emit(e)}catch(e){console.error("文件上传错误:",e),this.clearSelectedFile(),x.captureError(e,{action:"uploadFile",component:"pcm-mnms-zp-modal",title:"文件上传失败"}),l.emitError({error:e,message:"文件上传失败，请重试"})}finally{this.isUploading=!1}}}render(){var c,u,p,h,m,f,g;if(!this.isOpen)return null;const e={zIndex:String(this.zIndex)},o={"modal-container":!0,fullscreen:this.fullscreen,"pc-layout":!0},n={"modal-overlay":!0,"fullscreen-overlay":this.fullscreen},b=this.conversationId&&!this.showChatModal,r=!!(this.customInputs&&this.customInputs.job_info),d=!!(this.customInputs&&this.customInputs.file_url),v=!!((c=this.customInputs)!=null&&c.file_url&&((u=this.customInputs)!=null&&u.job_info));return i("div",{class:n,style:e},i("div",{class:o},this.isShowHeader&&i("div",{class:"modal-header"},i("div",{class:"header-left"},this.icon&&i("img",{src:this.icon,class:"header-icon",alt:"应用图标"}),i("div",null,this.modalTitle)),this.isNeedClose&&i("button",{class:"close-button",onClick:this.handleClose},i("span",null,"×"))),!this.showChatModal&&!this.conversationId&&!v&&i("div",{class:"input-container"},!r&&i("div",{class:"jd-input-section"},i("label",{htmlFor:"job-description"},"请输入职位描述 (JD)"),i("textarea",{id:"job-description",class:"job-description-textarea",placeholder:"请输入职位描述，包括职责、要求等信息...",rows:6,value:this.jobDescription,onInput:this.handleJobDescriptionChange})),!d&&i("div",{class:"resume-upload-section"},i("label",null,"上传简历"),i("div",{class:"upload-area",onClick:this.handleUploadClick},this.selectedFile?i("div",{class:"file-item"},i("div",{class:"file-item-content"},i("span",{class:"file-icon"},"📝"),i("span",{class:"file-name"},this.selectedFile.name)),i("button",{class:"remove-file",onClick:w=>{w.stopPropagation(),this.clearSelectedFile()}},"×")):i("div",{class:"upload-placeholder"},i("img",{src:"https://pub.pincaimao.com/static/web/images/home/<USER>"}),i("p",{class:"upload-text"},"点击上传简历"),i("p",{class:"upload-hint"},"支持 txt、markdown、pdf、docx、doc、md 格式")))),i("button",{class:"submit-button",disabled:!d&&!this.selectedFile||!r&&!this.jobDescription.trim()||this.isUploading||this.isSubmitting,onClick:this.handleStartInterview},this.isUploading?"上传中...":this.isSubmitting?"处理中...":"开始分析"),i("div",{class:"ai-disclaimer"},i("p",null,"所有内容均由AI生成仅供参考"),i("p",{class:"beian-info"},i("span",null,"中央网信办生成式人工智能服务备案号"),"：",i("a",{href:"https://www.pincaimao.com",target:"_blank",rel:"noopener noreferrer"},"Hunan-PinCaiMao-202412310003"))),i("input",{type:"file",class:"file-input",onChange:this.handleFileChange})),b&&i("div",{class:"loading-container"},i("div",{class:"loading-spinner"}),i("p",{class:"loading-text"},"正在加载对话...")),this.showChatModal&&i("div",null,i("pcm-app-chat-modal",{isOpen:!0,modalTitle:this.modalTitle,icon:this.icon,isShowHeader:this.isShowHeader,isNeedClose:this.isShowHeader,fullscreen:this.fullscreen,botId:"3022316191018907",conversationId:this.conversationId,defaultQuery:this.defaultQuery,enableVoice:!1,filePreviewMode:this.filePreviewMode,showCopyButton:this.showCopyButton,showFeedbackButtons:this.showFeedbackButtons,customInputs:this.conversationId?{}:{...this.customInputs,file_url:((p=this.customInputs)==null?void 0:p.file_url)||((h=this.uploadedFileInfo)==null?void 0:h.cos_key),file_name:((m=this.customInputs)==null?void 0:m.file_name)||((f=this.uploadedFileInfo)==null?void 0:f.file_name),job_info:((g=this.customInputs)==null?void 0:g.job_info)||this.jobDescription},interviewMode:this.interviewMode}))))}static get watchers(){return{token:["handleTokenChange"],isOpen:["handleIsOpenChange"]}}};B.style=j+D;export{B as pcm_mnms_zp_modal};
